import json
import os
import re
from typing import Any, Dict, List, Optional

import requests
from requests.adapters import HTT<PERSON>dapter
from strands import tool
from urllib3.util.retry import Retry

# Constants
DEFAULT_TIMEOUT = 30
MAX_RETRIES = 3
BACKOFF_FACTOR = 0.3

# Create a single session instance to reuse connections
_session = None
special_context_loop_node = """
Additional Information:

Behavior of the Loop Node:
The loop nodes behave differently than other nodes. 
the subgraph attached via current_item output handle is executed for each item in the list.
The result will be aggreegated and pass to the next node via final_results output handle.
"""
special_context_conditional_node = """
Behavior of the Conditional Node:
Input data is pass via the condition output handle

Behavior changes based on the input values.
if source = node_output:
    the input_data is used for the comparision. if it is the case then input_data must be a string.
if source = global_context:
    the global_content value is used for the comparision. if it is the case then global_context must be a string.
    Each condition has separate global_context.

if evaluation_strategy = first_match:
    The first matching condition, the input data is pass to the target node via that condition output handle.
if evaluation_strategy = all_match:
    Any conditions that matches, the input data is pass to the target node via that condition output handle.
"""


def get_session() -> requests.Session:
    """Get or create a singleton requests session with retry strategy."""
    global _session
    if _session is None:
        _session = requests.Session()

        # Define retry strategy
        retry_strategy = Retry(
            total=MAX_RETRIES,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS"],
            backoff_factor=BACKOFF_FACTOR,
        )

        # Mount adapter with retry strategy
        adapter = HTTPAdapter(max_retries=retry_strategy)
        _session.mount("http://", adapter)
        _session.mount("https://", adapter)

    return _session


def normalize_name(name: Optional[str]) -> str:
    """
    Normalize a name by replacing spaces and hyphens with underscores.

    Args:
        name: The name to normalize. Can be None.

    Returns:
        Normalized name string. Returns empty string if input is None.
    """
    if not name or not isinstance(name, str):
        return ""

    # Replace spaces and hyphens with underscores, then clean up
    normalized = re.sub(r"[\s\-]+", "_", name)
    normalized = re.sub(r"_+", "_", normalized)
    return normalized.strip("_")


def _validate_input_data(
    data: Optional[Dict[str, Any]], required_keys: List[str]
) -> Optional[str]:
    """Validate input data and return error message if invalid."""
    if not data or not isinstance(data, dict):
        return "Error: No valid data provided"

    for key in required_keys:
        if not data.get(key):
            return f"Error: {key.replace('_', ' ').title()} is required"

    return None


def _make_api_request(url: str) -> Optional[Dict[str, Any]]:
    """Make API request and return parsed JSON or None on error."""
    try:
        response = get_session().get(url, timeout=DEFAULT_TIMEOUT)
        response.raise_for_status()
        return response.json()
    except (requests.exceptions.RequestException, json.JSONDecodeError, Exception):
        return None


def context_component(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch component context from the API and format it for display.

    Args:
        data: Dictionary containing 'category' and 'name' keys

    Returns:
        Formatted context string or error message
    """
    # Input validation
    error = _validate_input_data(data, ["category", "name"])
    if error:
        return error

    category = data["category"]
    name_key = data["name"]

    url = os.environ.get("COMPONENT_URL")
    components = _make_api_request(url)

    if not components:
        return "Error: Failed to fetch components from API"

    # Navigate to component data with validation
    if not isinstance(components, dict) or category not in components:
        return f"Error: Category '{category}' not found"

    category_data = components[category]
    if not isinstance(category_data, dict) or name_key not in category_data:
        return f"Error: Component '{name_key}' not found in category '{category}'"

    component = category_data[name_key]
    if not isinstance(component, dict):
        return f"Error: Invalid component data for '{name_key}'"

    # Extract component data with safe defaults
    name = component.get("name", "N/A")
    description = component.get("description", "No description")
    inputs = (
        component.get("inputs", []) if isinstance(component.get("inputs"), list) else []
    )
    outputs = (
        component.get("outputs", [])
        if isinstance(component.get("outputs"), list)
        else []
    )

    return _build_component_context(name, description, inputs, outputs)


def _build_component_context(
    name: str,
    description: str,
    inputs: List[Dict[str, Any]],
    outputs: List[Dict[str, Any]],
) -> str:
    """Build formatted context string for component."""
    context_parts = [
        f"Name : {name}",
        f"Description : {description}",
        f"OriginalType : {name}",
        "Type : Component",
        "",
        "Inputs :-",
    ]

    # Process inputs
    for input_item in inputs:
        if not isinstance(input_item, dict):
            continue

        context_parts.extend(_format_input_item(input_item))

    context_parts.append("Outputs :-")

    # Process outputs
    for output_item in outputs:
        if not isinstance(output_item, dict):
            continue

        context_parts.extend(_format_output_item(output_item))
    if name == "LoopNode":
        context_parts.append(special_context_loop_node)
    if name == "ConditionalNode":
        context_parts.append(special_context_conditional_node)
    return "\n".join(context_parts)


def _format_input_item(item: Dict[str, Any]) -> List[str]:
    """Format a single input item."""
    parts = [
        f"Input Name : {item.get('name', 'N/A')}",
        f"Input Info : {item.get('info', '')}",
        f"Input Type : {item.get('input_type', '')}",
    ]

    # Add input types if available
    input_types = item.get("input_types")
    if input_types and isinstance(input_types, list):
        parts.append(f"Input Types : {', '.join(str(t) for t in input_types)}")

    # Add boolean flags
    flags = ["required", "is_handle", "is_list", "real_time_refresh", "advanced"]
    flag_labels = ["Required", "Handle", "List", "Real Time Refresh", "Advanced"]

    for flag, label in zip(flags, flag_labels):
        if item.get(flag):
            parts.append(label)

    # Add default value
    if item.get("value") is not None:
        parts.append(f"Default Value : {item['value']}")

    # Add options
    options = item.get("options")
    if options and isinstance(options, list):
        parts.append(f"Options : {', '.join(str(opt) for opt in options)}")

    # Add requirement rules
    if item.get("requirement_rules"):
        parts.extend(_format_rules(item, "requirement"))

    # Add visibility rules
    if item.get("visibility_rules"):
        parts.extend(_format_rules(item, "visibility"))

    parts.append("")  # Empty line separator
    return parts


def _format_output_item(item: Dict[str, Any]) -> List[str]:
    """Format a single output item."""
    parts = [
        f"Output Name : {item.get('name', 'N/A')}",
        f"Output Type : {item.get('output_type', '')}",
    ]

    if item.get("semantic_type"):
        parts.append(f"Semantic Type : {item['semantic_type']}")

    if item.get("method"):
        parts.append(f"Method : {item['method']}")

    parts.append("")  # Empty line separator
    return parts


def _format_rules(item: Dict[str, Any], rule_type: str) -> List[str]:
    """Format requirement or visibility rules."""
    rules_key = f"{rule_type}_rules"
    logic_key = f"{rule_type}_logic"

    rules = item.get(rules_key, [])
    logic = item.get(logic_key, "AND")

    if not rules:
        return []

    parts = [f"{rule_type.title()} Rules"]

    if rule_type == "requirement":
        parts.append("Input is required if ")
    else:
        parts.append("Input requires")

    rule_strings = []
    for rule in rules:
        if isinstance(rule, dict):
            rule_str = " ".join(
                [
                    rule.get("field_name", ""),
                    "to be",
                    rule.get("operator", ""),
                    rule.get("field_value", ""),
                ]
            )
            rule_strings.append(rule_str)

    if rule_strings:
        parts.append(f" {logic} ".join(rule_strings))

    return parts


def workflow_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch workflow context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' key for workflow ID

    Returns:
        Formatted context string or error message
    """
    # Input validation
    error = _validate_input_data(data, ["type_id"])
    if error:
        return error

    workflow_id = data["type_id"]
    if not isinstance(workflow_id, str):
        return f"Error: Workflow ID must be a string, got {type(workflow_id).__name__}"

    # url = f"{BASE_API_URL}/marketplace/workflows/{workflow_id}"
    url = os.environ.get("WORKFLOW_URL").format(workflow_id)
    workflow_data = _make_api_request(url)

    if not workflow_data:
        return f"Error: Failed to fetch workflow {workflow_id}"

    # Navigate to workflow data with validation
    workflow = workflow_data.get("workflow")
    if not isinstance(workflow, dict):
        return f"Error: No valid workflow found for ID '{workflow_id}'"

    # Extract workflow data with safe defaults
    name = workflow.get("name", "N/A")
    description = workflow.get("description", "No description")
    inputs = (
        workflow.get("start_nodes", [])
        if isinstance(workflow.get("start_nodes"), list)
        else []
    )

    return _build_workflow_context(name, description, workflow_id, inputs)


def _build_workflow_context(
    name: str, description: str, workflow_id: str, inputs: List[Dict[str, Any]]
) -> str:
    """Build formatted context string for workflow."""
    # Define standard workflow outputs
    standard_outputs = [
        {"name": "execution_status", "output_type": "string"},
        {"name": "workflow_execution_id", "output_type": "string"},
        {"name": "message", "output_type": "string"},
    ]

    context_parts = [
        f"Name : {name}",
        f"Description : {description}",
        f"OriginalType : workflow-{workflow_id}",
        "Type : Workflow",
        "",
        "Inputs :-",
    ]

    # Process inputs
    for input_item in inputs:
        if not isinstance(input_item, dict):
            continue

        field_name = input_item.get("field", "N/A")
        field_type = input_item.get("type", "N/A")

        context_parts.extend(
            [
                f"Input Name : {field_name}",
                f"Input Info : {field_name}",
                f"Input Type : {field_type}",
                "Required",
                "Handle",
                "",
            ]
        )

    context_parts.append("Outputs :-")

    # Process standard outputs
    for output_item in standard_outputs:
        context_parts.extend(
            [
                f"Output Name : {output_item['name']}",
                f"Output Type : {output_item['output_type']}",
                "",
            ]
        )

    return "\n".join(context_parts)


def mcp_context(data: Optional[Dict[str, Any]]) -> str:
    """
    Fetch MCP context from the API and format it for display.

    Args:
        data: Dictionary containing 'id' and 'name' keys for MCP ID and tool name

    Returns:
        Formatted context string or error message
    """
    # Input validation
    error = _validate_input_data(data, ["type_id", "name"])
    if error:
        return error

    mcp_id = data["type_id"]
    tool_name = data["name"]

    if not isinstance(mcp_id, str):
        return f"Error: MCP ID must be a string, got {type(mcp_id).__name__}"

    if not isinstance(tool_name, str):
        return f"Error: Tool name must be a string, got {type(tool_name).__name__}"

    url = os.environ.get("MCP_URL").format(mcp_id)
    mcp_data = _make_api_request(url)

    if not mcp_data:
        return f"Error: Failed to fetch MCP {mcp_id}"

    # Navigate to MCP data with validation
    mcp = mcp_data.get("mcp", {})
    if not isinstance(mcp, dict):
        return f"Error: Invalid MCP data structure for '{mcp_id}'"

    # Extract and find the specific tool
    mcp_tools_config = mcp.get("mcp_tools_config", {})
    tools = (
        mcp_tools_config.get("tools", []) if isinstance(mcp_tools_config, dict) else []
    )

    if not isinstance(tools, list):
        tools = []

    # Find the specific tool
    tool = next(
        (t for t in tools if isinstance(t, dict) and t.get("name") == tool_name), None
    )

    if not tool:
        available_tools = [
            t.get("name", "Unknown") for t in tools if isinstance(t, dict)
        ]
        return f"Error: Tool '{tool_name}' not found in MCP '{mcp_id}'. Available tools: {', '.join(available_tools)}"

    # Extract data safely
    mcp_name = mcp.get("name", "N/A")
    name = tool.get("name", "N/A")
    description = tool.get("description", "No description")
    input_schema = (
        tool.get("input_schema", {})
        if isinstance(tool.get("input_schema"), dict)
        else {}
    )
    output_schema = (
        tool.get("output_schema", {})
        if isinstance(tool.get("output_schema"), dict)
        else {}
    )

    # Generate original_type safely
    original_type = f"MCP_{normalize_name(f'{mcp_name} - {name}')}"

    return _build_mcp_context(
        name, description, original_type, mcp_id, tool_name, input_schema, output_schema
    )


def _build_mcp_context(
    name: str,
    description: str,
    original_type: str,
    mcp_id: str,
    tool_name: str,
    input_schema: Dict[str, Any],
    output_schema: Dict[str, Any],
) -> str:
    """Build formatted context string for MCP."""
    context_parts = [
        f"Name : {name}",
        f"Description : {description}",
        f"OriginalType : {original_type}",
        "Type : MCP",
        f"MCP_id : {mcp_id}",
        f"ToolName : {tool_name}",
        "",
        "Inputs :-",
    ]

    # Process input schema
    properties = input_schema.get("properties", {})
    required_inputs = input_schema.get("required", [])
    defs = input_schema.get("$defs", {})

    if isinstance(properties, dict):
        for prop_name, prop_details in properties.items():
            if isinstance(prop_details, dict):
                context_parts.extend(
                    _format_mcp_input_property(
                        prop_name, prop_details, required_inputs, defs
                    )
                )

    context_parts.append("Outputs :-")

    # Process output schema
    if not output_schema:
        output_schema = {
            "properties": {
                "result": {"name": "result", "type": "Any", "description": "Result"}
            }
        }

    output_properties = output_schema.get("properties", {})
    if isinstance(output_properties, dict):
        for output_name, output_details in output_properties.items():
            if isinstance(output_details, dict):
                context_parts.extend(
                    [
                        f"Output Name : {output_name}",
                        f"Output Info : {output_details.get('description', '')}",
                        f"Output Type : {output_details.get('type', '')}",
                        "",
                    ]
                )

    return "\n".join(context_parts)


def _format_mcp_input_property(
    prop_name: str,
    prop_details: Dict[str, Any],
    required_inputs: List[str],
    defs: Dict[str, Any],
) -> List[str]:
    """Format a single MCP input property."""
    parts = [
        f"Input Name : {prop_name}",
        f"Input Info : {prop_details.get('description', '')}",
    ]

    # Determine property type and metadata
    prop_type, options, property_scheme, items, is_array = _extract_property_info(
        prop_details, defs
    )

    if prop_type:
        parts.append(f"Input Type : {prop_type}")

    # Add metadata
    if prop_name in required_inputs:
        parts.append("Required")
    parts.append("Handle")

    if is_array:
        parts.append("List")

    if prop_details.get("default") is not None:
        parts.append(f"Default Value : {prop_details['default']}")

    if options:
        parts.append(f"Options : {', '.join(str(opt) for opt in options)}")

    # Handle nested properties
    if property_scheme and isinstance(property_scheme, dict):
        parts.append("Properties :-")
        for nested_name, nested_prop in property_scheme.items():
            if isinstance(nested_prop, dict):
                parts.extend(_format_nested_property(nested_name, nested_prop))

    if items:
        parts.append(f"Items : {items}")

    parts.append("")  # Empty line separator
    return parts


def _extract_property_info(prop_details: Dict[str, Any], defs: Dict[str, Any]) -> tuple:
    """Extract type information from property details."""
    prop_type = prop_details.get("type", "Any")
    options = None
    property_scheme = None
    items = None
    is_array = False

    if prop_type:
        if prop_type == "object":
            property_scheme = prop_details.get("properties", {})
        elif prop_type == "array":
            items = prop_details.get("items")
            is_array = True
        if "enum" in prop_details:
            options = prop_details["enum"]
    elif "anyOf" in prop_details:
        prop_type, options, property_scheme, items, is_array = _handle_any_of(
            prop_details["anyOf"], defs
        )
    elif "$ref" in prop_details:
        prop_type, options, property_scheme, items, is_array = _handle_ref(
            prop_details["$ref"], defs
        )

    return prop_type, options, property_scheme, items, is_array


def _handle_any_of(any_of_list: List[Dict[str, Any]], defs: Dict[str, Any]) -> tuple:
    """Handle anyOf schema definitions."""
    if not isinstance(any_of_list, list):
        return "Any", None, None, None, False

    types = []
    options = None
    property_scheme = None
    items = None
    is_array = False

    for item in any_of_list:
        if not isinstance(item, dict):
            continue

        if "type" in item and item["type"] != "null":
            item_type = item["type"]
            types.append(item_type)

            if item_type == "object":
                property_scheme = item.get("properties", {})
            elif item_type == "array":
                items = item.get("items")
                is_array = True
        elif "$ref" in item:
            ref_type, ref_options, ref_scheme, ref_items, ref_array = _handle_ref(
                item["$ref"], defs
            )
            if ref_type:
                types.append(ref_type)
                if ref_options:
                    options = ref_options
                if ref_scheme:
                    property_scheme = ref_scheme
                if ref_items:
                    items = ref_items
                if ref_array:
                    is_array = True

    return (
        ", ".join(types) if types else "Any",
        options,
        property_scheme,
        items,
        is_array,
    )


def _handle_ref(ref_path: str, defs: Dict[str, Any]) -> tuple:
    """Handle $ref schema definitions."""
    try:
        ref_key = ref_path.split("/")[-1]
        ref = defs.get(ref_key, {})

        if not isinstance(ref, dict):
            return "Any", None, None, None, False

        ref_type = ref.get("type", "Any")
        options = ref.get("enum") if isinstance(ref.get("enum"), list) else None
        property_scheme = ref.get("properties", {}) if ref_type == "object" else None
        items = ref.get("items") if ref_type == "array" else None
        is_array = ref_type == "array"

        return ref_type, options, property_scheme, items, is_array
    except Exception:
        return "Any", None, None, None, False


def _format_nested_property(name: str, prop: Dict[str, Any]) -> List[str]:
    """Format nested property information."""
    parts = [
        f"> Property Name : {name}",
        f"> Property Info : {prop.get('description', '')}",
    ]

    if prop.get("type"):
        parts.append(f"> Property Type : {prop['type']}")
    elif prop.get("anyOf") and isinstance(prop["anyOf"], list):
        types = [
            k.get("type")
            for k in prop["anyOf"]
            if isinstance(k, dict) and k.get("type") != "null"
        ]
        if types:
            parts.append(f"> Property Type : {', '.join(types)}")

    if prop.get("default") is not None:
        parts.append(f"> Property Default Value : {prop['default']}")

    if prop.get("enum") and isinstance(prop["enum"], list):
        parts.append(f"> Property Options : {', '.join(str(e) for e in prop['enum'])}")

    parts.append("> ")
    return parts


context_helpers = {
    "component": context_component,
    "workflow": workflow_context,
    "mcp_tool": mcp_context,
}


@tool(
    name="get_context",
    description="Function take a item return by the RAG search and as a dictionary not the string and return the context, inputs and output of the node.",
)
def get_context(node_info: dict) -> str:
    return context_helpers[node_info["type"]](node_info)
