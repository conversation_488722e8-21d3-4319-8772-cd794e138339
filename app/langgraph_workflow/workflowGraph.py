import math
from typing import Optional

import numpy as np
from pydantic import BaseModel, Field

from app.langgraph_workflow.utils.layout import rectangle_layout_with_separation
from app.langgraph_workflow.utils.node import get_component_node, get_mcp_node, get_workflow_node, Node

def _evaluate_rule(rule: dict, config: dict) -> bool:
    field_name = rule.get("field_name")
    operator = rule.get("operator")
    field_value = rule.get("field_value")
    if field_name not in config:
        return False
    if operator == "equals":
        return config[field_name] == field_value
    elif operator == "not_equals":
        return config[field_name] != field_value
    elif operator == "greater_than":
        return config[field_name] > field_value
    elif operator == "less_than":
        return config[field_name] < field_value
    elif operator == "greater_than_or_equal":
        return config[field_name] >= field_value
    elif operator == "less_than_or_equal":
        return config[field_name] <= field_value
    else:
        return False

def evaluate_rules_logic(logic: str, rules: list, config: dict) -> bool: ## TODO
    if rules is None:
        return True
    if logic == "AND":
        return all(_evaluate_rule(rule, config) for rule in rules)
    elif logic == "OR":
        return any(_evaluate_rule(rule, config) for rule in rules)
    else:
        return False

def _find_component(nodes, edges, start_node):
    """
    Find all nodes in the connected component that contains the start_node.

    Args:
        nodes: Set of node IDs in the graph
        edges: list of edge dictionaries with 'source' and 'target' keys
        start_node: The node ID to start the search from

    Returns:
        Set of node IDs that are in the same connected component as start_node
    """
    if start_node not in nodes:
        return set()

    # Build adjacency list for undirected graph
    adjacency = {node: set() for node in nodes}
    for edge in edges:
        source = edge.get("source")
        target = edge.get("target")
        if source in nodes and target in nodes:
            adjacency[source].add(target)
            adjacency[target].add(source)

    # Perform BFS/DFS to find connected component
    visited = set()
    queue = [start_node]
    visited.add(start_node)

    while queue:
        current = queue.pop(0)
        for neighbor in adjacency[current]:
            if neighbor not in visited:
                visited.add(neighbor)
                queue.append(neighbor)

    return visited

class Edge(BaseModel):
    id: str
    source: str
    sourceHandle: str
    target: str
    targetHandle: str
    animated: bool = True
    style: dict = {"strokeWidth": 2, "zIndex": 5}
    type: str = "default"
    selected: bool = False


class WorkflowGraph(BaseModel):
    nodes: dict[str, Node]
    edges: dict[str, Edge]

    def add_node(
        self,
        node_id: str,
        label: str,
        OriginalType: str,
        type: str,
        position: tuple[float, float],
        parameters: dict,
        mcp_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        tool_name: Optional[str] = None,
    ):
        if type == "component":
            node = get_component_node(
                node_id, label, OriginalType, position, parameters
            )
        elif type == "workflow":
            node = get_workflow_node(
                node_id, label, OriginalType, position, parameters, workflow_id
            )
        elif type == "mcp":
            node = get_mcp_node(
                node_id, label, OriginalType, position, parameters, mcp_id, tool_name
            )
        else:
            raise ValueError(f"Unknown node type: {type}")
        if node_id in self.nodes:
            raise ValueError(f"Node {node_id} already exists")
        inputs = node.data.definition["inputs"]
        config = node.data.config
        for input_item in inputs:
            if (
                    isinstance(input_item, dict)
                    and not input_item.get("required")
                    and input_item.get("name") not in config
                    and not input_item.get("is_handle")
                ):
                requirement_logic = input_item.get("requirement_logic", "AND")
                requirement_rules = input_item.get("requirement_rules", [])
                if evaluate_rules_logic(
                    requirement_logic, requirement_rules, config
                ):
                    if input_item["default"] is not None:
                        node.data.config[input_item["name"]] = input_item["default"]
                    else:
                        ValueError(f"Missing required parameter: {input_item['name']}")
        self.nodes[node_id] = node

    def add_edge(self, source: str, sourceHandle: str, target: str, targetHandle: str):
        if source not in self.nodes:
            raise ValueError(f"Source node {source} does not exist")
        if target not in self.nodes:
            raise ValueError(f"Target node {target} does not exist")
        if source == target:
            raise ValueError("Cannot connect node to itself")
        if sourceHandle not in [
            output["name"]
            for output in self.nodes[source].data.definition["outputs"]
        ]:
            raise ValueError(
                f"Source handle {sourceHandle} does not exist in source node {source}"
            )
        if targetHandle not in [
            input["name"]
            for input in self.nodes[target].data.definition["inputs"]
        ]:
            raise ValueError(
                f"Target handle {targetHandle} does not exist in target node {target}"
            )
        source_type = next(
            (
                output["output_type"]
                for output in self.nodes[source].data.definition["outputs"]
                if output["name"] == sourceHandle
            ),
            None,
        )
        target_type = next(
            (
                input["input_type"]
                for input in self.nodes[target].data.definition["inputs"]
                if input["name"] == targetHandle
            ),
            None,
        )
        type_compatibility = {
            "string": ["string", "object", "any"],
            "number": ["number", "string", "any"],
            "boolean": ["boolean", "string", "any"],
            "object": ["object", "string", "any"],
            "array": ["array", "object", "any"],
            "any": ["string", "number", "boolean", "object", "array", "any"],
        }

        is_compatible = target_type in type_compatibility.get(source_type, [])
        if not is_compatible:
            raise ValueError(
                f"Type mismatch: Cannot connect {source_type} to {target_type}"
            )
        warnings = []
        if source_type == "object" and target_type == "string":
            warnings.append("Object to string conversion may require serialization")
        if source_type == "array" and target_type != "array":
            warnings.append("Array to non-array conversion may lose data")

        for edge in self.edges.values():
            if (
                edge.source == source
                and edge.sourceHandle == sourceHandle
                and edge.target == target
                and edge.targetHandle == targetHandle
            ):
                raise ValueError(f"Edge already exists: {edge.id}")
            if edge.target == target and edge.targetHandle == targetHandle and targetHandle != "tools":
                raise ValueError(f"Target handle {targetHandle} is already connected")

        target_node = self.nodes[target]
        target_config = target_node.data.config
        target_inputs = target_node.data.definition["inputs"]
        targetHandle_input = next(
            (input for input in target_inputs if input["name"] == targetHandle), None
        )
        targetHandle_visibility_rules = targetHandle_input.get("visibility_rules")
        targetHandle_requirement_rules = targetHandle_input.get("requirement_rules")
        if not evaluate_rules_logic(
            targetHandle_visibility_rules, targetHandle_visibility_rules, target_config
        ):
            raise ValueError(f"Target handle {targetHandle} is not visible")

        edge = Edge(
            id=f"reactflow__edge{source}{sourceHandle}-{target}{targetHandle}",
            source=source,
            sourceHandle=sourceHandle,
            target=target,
            targetHandle=targetHandle,
        )
        self.edges[edge.id] = edge
        return warnings

    def delete_node(self, node_id: str):
        if node_id not in self.nodes:
            raise ValueError(f"Node {node_id} does not exist")
        del self.nodes[node_id]
        edges_to_delete = [
            edge_id
            for edge_id, edge in self.edges.items()
            if edge.source == node_id or edge.target == node_id
        ]
        for edge_id in edges_to_delete:
            del self.edges[edge_id]

    def delete_edge(
        self, source: str, sourceHandle: str, target: str, targetHandle: str
    ):
        edge_id = f"reactflow__edge{source}{sourceHandle}-{target}{targetHandle}"
        if edge_id not in self.edges:
            raise ValueError(f"Edge {edge_id} does not exist")
        del self.edges[edge_id]

    def update_node(self, node_id: str, parameters: dict):
        if node_id not in self.nodes:
            raise ValueError(f"Node {node_id} does not exist")
        self.nodes[node_id].data.config = parameters

    def get_graph_repr(self):
        nodes = []
        edges = []
        for node_id, node in self.nodes.items():
            node = {
                "node_id": node_id,
                "label": node.data.label,
                "OriginalType": node.data.originalType,
                "position": node.position,
                "parameters": node.data.config,
            }
            nodes.append(node)
        for edge_id, edge in self.edges.items():
            edge = {
                "source": edge.source,
                "sourceHandle": edge.sourceHandle,
                "target": edge.target,
                "targetHandle": edge.targetHandle,
            }
            edges.append(edge)
        return {"nodes": nodes, "edges": edges}

    def compile(self):
        required_parameters = []
        for node_id, node in self.nodes.items():
            inputs = node.data.definition["inputs"]
            config = node.data.config
            for input_item in inputs:
                if (
                    input_item["default"] is not None
                    and input_item["name"] not in config
                ):
                    config[input_item["name"]] = input_item["default"]
            self.update_node(node_id, config)
            for input_item in inputs:
                if (
                    isinstance(input_item, dict)
                    and input_item.get("required")
                    and input_item.get("name") not in config
                    and input_item.get("is_handle")
                ):
                    required_parameters.append((node_id, input_item["name"]))
                elif (
                    isinstance(input_item, dict)
                    and not input_item.get("required")
                    and input_item.get("name") not in config
                    and input_item.get("is_handle")
                ):
                    requirement_logic = input_item.get("requirement_logic", "AND")
                    requirement_rules = input_item.get("requirement_rules", [])
                    if evaluate_rules_logic(
                        requirement_logic, requirement_rules, config
                    ):
                        required_parameters.remove((node_id, input_item["name"]))
        for _, edge in self.edges.items():
            if (edge.target, edge.targetHandle) in required_parameters:
                required_parameters.remove((edge.target, edge.targetHandle))
        for target, targetHandle in required_parameters:
            self.add_edge("start-node", "flow", target, targetHandle)
        node_ids = _find_component(set(self.nodes.keys()), self.edges, "start-node")
        workflow = {
            "nodes": [self.nodes[node_id].model_dump() for node_id in node_ids],
            "edges": [self.edges[edge_id].model_dump() for edge_id in self.edges],
        }

        rectangles = []
        for node in workflow["nodes"]:
            rectangles.append(
                {
                    "id": node["id"],
                    "x": node["position"]["x"],
                    "y": node["position"]["y"],
                    "w": node["width"],
                    "h": node["height"],
                }
            )
        separation = 100.0
        new_layout = rectangle_layout_with_separation(
            rectangles, separation=separation, fixed_node_id="start-node"
        )
        new_layout = {
            rect["id"]: {"x": int(rect["x"]), "y": int(rect["y"])}
            for rect in new_layout
        }
        for node in workflow["nodes"]:
            node["position"] = new_layout[node["id"]]
        return workflow
